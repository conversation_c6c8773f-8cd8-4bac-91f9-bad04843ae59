<template>
    <div class="min-h-screen bg-gray-50 py-12">
        <div class="max-w-4xl mx-auto px-4">
            <h1 class="text-3xl font-bold text-gray-900 mb-8">Button Component Demo</h1>
            
            <!-- Basic Buttons -->
            <section class="mb-12">
                <h2 class="text-2xl font-semibold text-gray-800 mb-6">Basic Buttons</h2>
                <div class="flex flex-wrap gap-4">
                    <Button>Default Button</Button>
                    <Button color="action">Action Button</Button>
                    <Button color="success">Success Button</Button>
                    <Button color="warning">Warning Button</Button>
                    <Button color="error">Error But<PERSON></Button>
                </div>
            </section>

            <!-- Buttons with Left Icons -->
            <section class="mb-12">
                <h2 class="text-2xl font-semibold text-gray-800 mb-6">Buttons with Left Icons</h2>
                <div class="flex flex-wrap gap-4">
                    <Button :icon-left="IconUser">Profile</Button>
                    <Button :icon-left="IconBrandGoogle" color="default">Sign in with Google</Button>
                    <Button :icon-left="IconSettings" color="action">Settings</Button>
                    <Button :icon-left="IconMail" color="success">Send Email</Button>
                </div>
            </section>

            <!-- Buttons with Right Icons -->
            <section class="mb-12">
                <h2 class="text-2xl font-semibold text-gray-800 mb-6">Buttons with Right Icons</h2>
                <div class="flex flex-wrap gap-4">
                    <Button :icon-right="IconArrowRight">Continue</Button>
                    <Button :icon-right="IconArrowRight" color="action">Next Step</Button>
                    <Button :icon-right="IconArrowRight" color="success">Proceed</Button>
                </div>
            </section>

            <!-- Buttons with Both Icons -->
            <section class="mb-12">
                <h2 class="text-2xl font-semibold text-gray-800 mb-6">Buttons with Both Icons</h2>
                <div class="flex flex-wrap gap-4">
                    <Button :icon-left="IconUser" :icon-right="IconArrowRight">User Profile</Button>
                    <Button :icon-left="IconSettings" :icon-right="IconArrowRight" color="action">Configure</Button>
                </div>
            </section>

            <!-- Different Sizes -->
            <section class="mb-12">
                <h2 class="text-2xl font-semibold text-gray-800 mb-6">Different Sizes with Icons</h2>
                <div class="flex flex-wrap items-center gap-4">
                    <Button :icon-left="IconBrandGoogle" size="md">Medium</Button>
                    <Button :icon-left="IconBrandGoogle" size="lg">Large</Button>
                    <Button :icon-left="IconBrandGoogle" size="xl">Extra Large</Button>
                </div>
            </section>

            <!-- Rounded Buttons -->
            <section class="mb-12">
                <h2 class="text-2xl font-semibold text-gray-800 mb-6">Rounded Buttons with Icons</h2>
                <div class="flex flex-wrap gap-4">
                    <Button :icon-left="IconBrandGoogle" rounded>Sign in with Google</Button>
                    <Button :icon-right="IconArrowRight" color="action" rounded>Get Started</Button>
                    <Button :icon-left="IconUser" :icon-right="IconArrowRight" color="success" rounded>Complete Profile</Button>
                </div>
            </section>

            <!-- Icon Only Buttons -->
            <section class="mb-12">
                <h2 class="text-2xl font-semibold text-gray-800 mb-6">Icon Only Buttons</h2>
                <div class="flex flex-wrap gap-4">
                    <Button :icon-left="IconUser" />
                    <Button :icon-left="IconSettings" color="action" />
                    <Button :icon-left="IconMail" color="success" />
                    <Button :icon-left="IconClose" color="error" />
                </div>
            </section>

            <!-- Disabled States -->
            <section class="mb-12">
                <h2 class="text-2xl font-semibold text-gray-800 mb-6">Disabled States</h2>
                <div class="flex flex-wrap gap-4">
                    <Button :icon-left="IconUser" disabled>Disabled</Button>
                    <Button :icon-left="IconBrandGoogle" :icon-right="IconArrowRight" color="action" disabled>Disabled with Icons</Button>
                </div>
            </section>
        </div>
    </div>
</template>

<script setup>
import Button from "@/Components/Button/Button.vue";
import IconUser from "@/Components/Icons/IconUser.vue";
import IconBrandGoogle from "@/Components/Icons/IconBrandGoogle.vue";
import IconSettings from "@/Components/Icons/IconSettings.vue";
import IconMail from "@/Components/Icons/IconMail.vue";
import IconArrowRight from "@/Components/Icons/IconArrowRight.vue";
import IconClose from "@/Components/Icons/IconClose.vue";
</script>
