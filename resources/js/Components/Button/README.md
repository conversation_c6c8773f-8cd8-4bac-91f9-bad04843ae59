# Button Component

The Button component is a versatile UI component that supports icons on either side of the button text.

## Basic Usage

```vue
<template>
  <Button>Click me</Button>
</template>

<script setup>
import Button from '@/Components/Button/Button.vue'
</script>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `disabled` | Boolean | `false` | Disables the button |
| `transparent` | Boolean | `false` | Makes the button background transparent |
| `rounded` | Boolean | `false` | Applies rounded corners |
| `size` | String | `"md"` | Button size: `"md"`, `"lg"`, `"xl"` |
| `color` | String | `"default"` | Button color theme |
| `iconLeft` | Component | `null` | Vue component to display on the left side |
| `iconRight` | Component | `null` | Vue component to display on the right side |

### Color Options
- `"default"` - Default styling
- `"black"` - Black theme
- `"informationPlus"` - Information plus theme
- `"information"` - Information theme
- `"success"` - Success theme
- `"warning"` - Warning theme
- `"error"` - Error theme
- `"action"` - Action theme

## Icon Usage

### Left Icon
```vue
<template>
  <Button :icon-left="IconUser">Profile</Button>
</template>

<script setup>
import Button from '@/Components/Button/Button.vue'
import IconUser from '@/Components/Icons/IconUser.vue'
</script>
```

### Right Icon
```vue
<template>
  <Button :icon-right="IconArrowRight">Continue</Button>
</template>

<script setup>
import Button from '@/Components/Button/Button.vue'
import IconArrowRight from '@/Components/Icons/IconArrowRight.vue'
</script>
```

### Both Icons
```vue
<template>
  <Button :icon-left="IconUser" :icon-right="IconArrowRight">
    User Profile
  </Button>
</template>

<script setup>
import Button from '@/Components/Button/Button.vue'
import IconUser from '@/Components/Icons/IconUser.vue'
import IconArrowRight from '@/Components/Icons/IconArrowRight.vue'
</script>
```

### Icon Only Button
```vue
<template>
  <Button :icon-left="IconSettings" />
</template>

<script setup>
import Button from '@/Components/Button/Button.vue'
import IconSettings from '@/Components/Icons/IconSettings.vue'
</script>
```

## Examples

### Google Sign In Button
```vue
<template>
  <Button 
    :icon-left="IconBrandGoogle" 
    color="default" 
    size="lg"
    rounded
  >
    Sign in with Google
  </Button>
</template>

<script setup>
import Button from '@/Components/Button/Button.vue'
import IconBrandGoogle from '@/Components/Icons/IconBrandGoogle.vue'
</script>
```

### Action Button with Arrow
```vue
<template>
  <Button 
    :icon-right="IconArrowRight" 
    color="action" 
    size="lg"
  >
    Get Started
  </Button>
</template>

<script setup>
import Button from '@/Components/Button/Button.vue'
import IconArrowRight from '@/Components/Icons/IconArrowRight.vue'
</script>
```

## CSS Classes

The component automatically applies appropriate CSS classes for icons:

- `.btn__icon` - Base icon styling
- `.btn__icon--left` - Left icon spacing (margin-right)
- `.btn__icon--right` - Right icon spacing (margin-left)
- `.btn__icon--md` - Medium icon size (16x16px)
- `.btn__icon--lg` - Large icon size (20x20px)
- `.btn__icon--xl` - Extra large icon size (24x24px)
- `.btn__content` - Content wrapper when icons are present

## Storybook

View all button variations and test the component in Storybook:

```bash
npm run storybook
```

Navigate to "Button" in the sidebar to see all examples including the new icon functionality.
