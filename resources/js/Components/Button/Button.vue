<template>
    <button type="button" class="btn" :class="['disabled:opacity-50 disabled:cursor-not-allowed',
        { 'btn--transparent': transparent },
        { 'rounded-3xl': rounded },
        sizeClass,
        colorClass,
    ]" :disabled="disabled">
        <!-- Left Icon -->
        <component
            v-if="iconLeft"
            :is="iconLeft"
            :class="['btn__icon btn__icon--left', iconSizeClass]"
        />

        <!-- Button Content -->
        <span v-if="$slots.default" :class="{ 'btn__content': hasIcons }">
            <slot></slot>
        </span>

        <!-- Right Icon -->
        <component
            v-if="iconRight"
            :is="iconRight"
            :class="['btn__icon btn__icon--right', iconSizeClass]"
        />
    </button>
</template>

<script setup>
import { computed } from "vue";
const props = defineProps({
    disabled: {
        type: Boolean,
        default: false,
    },
    transparent: {
        type: Boolean,
        default: false,
    },
    rounded: {
        type: Boolean,
        default: false,
    },
    size: {
        type: String,
        default: "md",
        validator: (value) => {
            return ["md", "lg", "xl"].includes(value);
        },
    },
    color: {
        type: String,
        default: "default",
        validator: (value) => {
            return [
                "default",
                "black",
                "informationPlus",
                "information",
                "success",
                "warning",
                "error",
                "action",
            ].includes(value);
        },
    },
    iconLeft: {
        type: [Object, Function],
        default: null,
    },
    iconRight: {
        type: [Object, Function],
        default: null,
    },
});

const sizeClass = computed(() => {
    return {
        "btn--md": props.size === "md",
        "btn--lg": props.size === "lg",
        "btn--xl": props.size === "xl",
    };
});

const colorClass = computed(() => {
    return {
        "btn--default": props.color === "default",
        "btn--black": props.color === "black",
        "btn--information-plus": props.color === "informationPlus",
        "btn--information": props.color === "information",
        "btn--success": props.color === "success",
        "btn--warning": props.color === "warning",
        "btn--error": props.color === "error",
        "btn--action": props.color === "action",
    };
});

const iconSizeClass = computed(() => {
    return {
        "btn__icon--md": props.size === "md",
        "btn__icon--lg": props.size === "lg",
        "btn__icon--xl": props.size === "xl",
    };
});

const hasIcons = computed(() => {
    return props.iconLeft || props.iconRight;
});
</script>
