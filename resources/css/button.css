@layer components {
    .btn {
        @apply inline-flex cursor-pointer items-center justify-center rounded border px-16 py-5 transition focus:outline-none focus:ring-2 focus:ring-offset-2;

        &:disabled {
            background: theme("colors.surface.disabled");
            border-color: theme("colors.border.disabled");
            color: theme("colors.text.disabled");
            cursor: default;
        }
    }

    /* Size varients */
    .btn--md {
        @apply text-sm-XL;
    }

    .btn--lg {
        @apply text-md-XL;
    }

    .btn--xl {
        @apply text-lg-XL;
    }

    /* Icon styles */
    .btn__icon {
        @apply flex-shrink-0;
    }

    .btn__icon--left {
        @apply mr-8;
    }

    .btn__icon--right {
        @apply ml-8;
    }

    .btn__icon--md {
        @apply w-16 h-16;
    }

    .btn__icon--lg {
        @apply w-20 h-20;
    }

    .btn__icon--xl {
        @apply w-24 h-24;
    }

    .btn__content {
        @apply flex-1 text-center;
    }

    /* Color varients */
    .btn--default {
        background: theme("colors.surface.page");
        border-color: theme("colors.border.primary");
        color: theme("colors.text.body");

        &.btn--transparent {
            color: theme("colors.text.body");
        }

        &:hover:not(:disabled) {
            background: theme("colors.surface.true-black");
            border-color: theme("colors.border.black");
            color: theme("colors.text.body-white");
        }

        &:focus {
            background: theme("colors.surface.true-black");
            border-color: theme("colors.border.black");
            color: theme("colors.text.body-white");
        }
        @apply focus:ring-border-black;
    }

    .btn--black {
        background: theme("colors.surface.page-inv");
        border-color: theme("colors.border.dark-black");
        color: theme("colors.text.body-white");

        &.btn--transparent {
            color: theme("colors.text.body");
        }

        &:hover:not(:disabled) {
            background: theme("colors.surface.page");
            border-color: theme("colors.border.primary");
            color: theme("colors.text.body");
        }

        &:focus {
            background: theme("colors.surface.page");
            border-color: theme("colors.border.black");
            color: theme("colors.text.body");
        }
        @apply focus:ring-border-black;
    }

    .btn--information-plus {
        background: theme("colors.surface.information-darkest");
        border-color: theme("colors.border.information-dark");
        color: theme("colors.text.body-always-white");

        &.btn--transparent {
            color: theme("colors.text.information");
        }

        &:hover:not(:disabled) {
            background: theme("colors.surface.information-dark");
            border-color: theme("colors.border.information");
            color: theme("colors.text.body-always-black");
        }

        &:focus {
            background: theme("colors.surface.information-darkest");
            border-color: theme("colors.border.information-dark");
            color: theme("colors.text.body-always-white");
        }
        @apply focus:ring-border-information-dark;
    }

    .btn--information {
        background: theme("colors.surface.information");
        border-color: theme("colors.border.information");
        color: theme("colors.text.information");

        &.btn--transparent {
            color: theme("colors.text.information");
        }

        &:hover:not(:disabled) {
            background: theme("colors.surface.information-dark");
            border-color: theme("colors.border.information");
            color: theme("colors.text.body");
        }

        &:focus {
            background: theme("colors.surface.information-darkest");
            border-color: theme("colors.border.information-dark");
            color: theme("colors.text.body-always-white");
        }
        @apply focus:ring-border-information-dark;
    }

    .btn--success {
        background: theme("colors.surface.success");
        border-color: theme("colors.border.success");
        color: theme("colors.text.success");

        &.btn--transparent {
            color: theme("colors.text.success");
        }

        &:hover:not(:disabled) {
            background: theme("colors.surface.success-dark");
            border-color: theme("colors.border.success-dark");
            color: theme("colors.text.body-always-white");
        }

        &:focus {
            background: theme("colors.surface.success-darkest");
            border-color: theme("colors.border.success");
            color: theme("colors.text.body-always-white");
        }
        @apply focus:ring-border-success;
    }

    .btn--warning {
        background: theme("colors.surface.warning");
        border-color: theme("colors.border.warning");
        color: theme("colors.text.warning");

        &.btn--transparent {
            color: theme("colors.text.warning-light");
        }

        &:hover:not(:disabled) {
            background: theme("colors.surface.warning-dark");
            border-color: theme("colors.border.warning-dark");
            color: theme("colors.text.body-always-white");
        }

        &:focus {
            background: theme("colors.surface.warning-darkest");
            border-color: theme("colors.border.warning");
            color: theme("colors.text.body-always-white");
        }
        @apply focus:ring-border-warning;
    }

    .btn--error {
        background: theme("colors.surface.error-dark");
        border-color: theme("colors.border.error-dark");
        color: theme("colors.text.body-white");

        &.btn--transparent {
            color: theme("colors.text.error");
        }

        &:hover:not(:disabled) {
            background: theme("colors.surface.error-darkest");
            border-color: theme("colors.border.error-dark");
            color: theme("colors.text.body-always-white");
        }

        &:focus {
            background: theme("colors.surface.error-darkest");
            border-color: theme("colors.border.error");
            color: theme("colors.text.body-always-white");
        }
        @apply focus:ring-border-error;
    }

    .btn--action {
        background: theme("colors.surface.action");
        border-color: theme("colors.border.action");
        color: theme("colors.text.body-always-white");

        &.btn--transparent {
            color: theme("colors.text.action");
        }

        &:hover:not(:disabled) {
            background: theme("colors.surface.action-hover");
            border-color: theme("colors.icon.action-light");
        }

        &:focus {
            background: theme("colors.surface.focus");
            border-color: theme("colors.border.focus");
        }
        @apply focus:ring-border-focus;
    }
}

.btn--transparent {
    background: transparent;
    border-color: transparent;
}

.btn--secondary {
    @apply flex items-center py-8 text-lg font-bold text-text-action-hover;
}
.btn--secondary:disabled {
    @apply text-text-disabled;
}
